"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// import OptimizedImage from '@/components/ui/optimized-image';
import { Download, Share2, X, Heart, Eye, Star } from 'lucide-react';
import { ColoringPage } from '@/types/coloring-category';

interface ColoringPageModalProps {
  page: ColoringPage | null;
  isOpen: boolean;
  onClose: () => void;
  relatedPages?: ColoringPage[];
}

export function ColoringPageModal({ page, isOpen, onClose, relatedPages = [] }: ColoringPageModalProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  // 键盘导航支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!page) return null;

  const handleDownload = async (format: 'pdf' | 'png') => {
    setIsDownloading(true);
    try {
      // 模拟下载过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = page.image_url;
      link.download = `${page.title.replace(/[^a-zA-Z0-9]/g, '-')}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log(`Downloaded ${page.title} as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleShare = async () => {
    setIsSharing(true);
    try {
      if (navigator.share) {
        await navigator.share({
          title: page.title,
          text: page.description,
          url: window.location.origin + '/' + page.seo_slug,
        });
      } else {
        // 回退到复制链接
        await navigator.clipboard.writeText(window.location.origin + '/' + page.seo_slug);
        alert('链接已复制到剪贴板！');
      }
    } catch (error) {
      console.error('Share failed:', error);
    } finally {
      setIsSharing(false);
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'hard': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0">
        <div className="relative">
          {/* 关闭按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 z-10 bg-white/80 hover:bg-white shadow-lg"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>

          {/* 主图片区域 */}
          <div className="relative bg-gradient-to-br from-gray-50 to-white p-8">
            <div className="max-w-2xl mx-auto">
              <img
                src={page.image_url}
                alt={page.alt_text || page.title}
                className="w-full h-auto rounded-lg shadow-2xl max-w-2xl mx-auto"
              />
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-6">
            <DialogHeader className="mb-6">
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1">
                  <DialogTitle className="text-2xl font-bold mb-2">{page.title}</DialogTitle>
                  <p className="text-muted-foreground mb-4">{page.description}</p>
                  
                  {/* 标签和统计 */}
                  <div className="flex flex-wrap items-center gap-2 mb-4">
                    <Badge className={`${getDifficultyColor(page.difficulty_level)} text-white`}>
                      {page.difficulty_level?.toUpperCase()}
                    </Badge>
                    {page.is_featured && (
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        <Star className="w-3 h-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {page.view_count || 0}
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Download className="w-3 h-3" />
                      {page.download_count || 0}
                    </Badge>
                  </div>

                  {/* 标签 */}
                  {page.tags && page.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-4">
                      {page.tags.slice(0, 5).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          #{tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </DialogHeader>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-3 mb-6">
              <Button
                onClick={() => handleDownload('png')}
                disabled={isDownloading}
                className="flex-1 sm:flex-none"
              >
                <Download className="w-4 h-4 mr-2" />
                {isDownloading ? '下载中...' : '下载 PNG'}
              </Button>
              <Button
                onClick={() => handleDownload('pdf')}
                disabled={isDownloading}
                variant="outline"
                className="flex-1 sm:flex-none"
              >
                <Download className="w-4 h-4 mr-2" />
                {isDownloading ? '下载中...' : '下载 PDF'}
              </Button>
              <Button
                onClick={handleShare}
                disabled={isSharing}
                variant="outline"
                className="flex-1 sm:flex-none"
              >
                <Share2 className="w-4 h-4 mr-2" />
                {isSharing ? '分享中...' : '分享'}
              </Button>
            </div>

            {/* 相关推荐 */}
            {relatedPages.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-4">相关涂色页</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {relatedPages.slice(0, 4).map((relatedPage) => (
                    <div
                      key={relatedPage.id}
                      className="group cursor-pointer"
                      onClick={() => {
                        // 这里可以切换到相关页面
                        console.log('Switch to related page:', relatedPage.id);
                      }}
                    >
                      <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
                        <img
                          src={relatedPage.thumbnail_url || relatedPage.image_url}
                          alt={relatedPage.alt_text || relatedPage.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        {relatedPage.is_featured && (
                          <div className="absolute top-2 right-2 bg-yellow-500 text-white p-1 rounded-full">
                            <Star className="w-3 h-3" />
                          </div>
                        )}
                      </div>
                      <h4 className="text-sm font-medium mt-2 line-clamp-2 group-hover:text-primary transition-colors">
                        {relatedPage.title}
                      </h4>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
